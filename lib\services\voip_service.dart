import 'dart:async';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/events/firebase_event.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/events/voip_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/enums/voip_sip_event_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/audio_device_service.dart';
import 'package:ddone/services/audio_session_service.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/foreground_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/janus_service.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:janus_client/janus_client.dart';

class VoipService with PrefsAware {
  // static final singleton pattern
  static final VoipService _instance = VoipService._internal();
  VoipService._internal()
      : janusService = sl.get<JanusService>(),
        callkitService = sl.get<CallkitService>(),
        _audioSessionService = sl.get<AudioSessionService>(),
        _audioDeviceService = sl.get<AudioDeviceService>(),
        _firebaseService = sl.get<FirebaseService>(),
        _hiveService = sl.get<HiveService>(),
        _eventBus = sl.get<IEventBus>() {
    _firebaseEventSubscription = _eventBus.on<FirebaseEvent>().listen((event) {
      log.t('FirebaseEvent evenbus: ${event.category}');
      if (event.category == 'end-call' || event.category == 'miss-call') {
        // same as janusService, refer to there for reason of having this.
        _onJanusSipHangUpEvent();
      }
    });
    _networkEventSubscription = _eventBus.on<NetworkEvent>().listen((event) {
      if (event.state is NetworkDisconnected) {
        log.t('VoipCubit - eventBus: networkEvent:$event');
        _hangupCallWhenNetworkDisconnect();
      }
    });
    if (isAndroid) {
      final service = FlutterBackgroundService();
      _fBroadcastEventSubscription = service.on(ForegroundMethod.broadcastEvent.name).listen((event) async {
        log.t('ForegroundService boardcastEvent: $event');
        foregroundVoipState = VoipState.fromJson(event!['event'] as Map<String, dynamic>);
        if (foregroundVoipState is VoipSipAcceptedSuccess) {
          await _onJanusSipAcceptedEvent();
        } else if (foregroundVoipState is VoipSipProgress) {
          await _onJanusSipProgressEvent();
        } else if (foregroundVoipState is VoipSipCalling) {
          await _onJanusSipCallingEvent();
        } else if (foregroundVoipState is VoipSipProceeding) {
          await _onJanusSipProceedingEvent();
        } else if (foregroundVoipState is VoipSipRinging) {
          await _onJanusSipRingingEvent();
        } else if (foregroundVoipState is VoipSipHungUp) {
          await _onJanusSipHangUpEvent();
        } else if (foregroundVoipState is VoipSipTransferCall) {
          await _onJanusTransferCallEvent();
        }
      });
      _fReceiveForegroundMasterIdSubscription =
          service.on(ForegroundMethod.receiveForegroundMasterId.name).listen((event) async {
        log.t('ForegroundService receiveForegroundMasterId: $event');
        _foregroundMasterId = event!['masterId'];
      });
      _fRetrieveMainMasterIdSubscription = service.on(ForegroundMethod.retrieveMainMasterId.name).listen((event) async {
        log.t('ForegroundService retrieveMainMasterId');
        await ForegroundService.invokeMethod(ForegroundMethod.receiveMainMasterId, {'masterId': _mainMasterId});
      });
      _fReceiveForegroundIsReadySubscription =
          service.on(ForegroundMethod.receiveForegroundIsReady.name).listen((event) async {
        log.t('ForegroundService receiveForegroundIsReady: $event');
        _foregroundIsReady = event!['isReady'] ?? false;
      });
      _fInformStopSubscription = service.on(ForegroundMethod.informStop.name).listen((event) async {
        log.t('ForegroundService informStop');
        _foregroundIsReady = false;
        _foregroundMasterId = null;
        foregroundVoipState = null;
        if (await isAppOpen()) {
          maintainConnection();
        }
      });
    }
  }
  factory VoipService() {
    return _instance;
  }

  late final JanusService janusService;
  late final CallkitService callkitService;
  late final AudioSessionService _audioSessionService;
  late final AudioDeviceService _audioDeviceService;
  late final FirebaseService _firebaseService;
  late final HiveService _hiveService;
  late final IEventBus _eventBus;

  StreamSubscription<FirebaseEvent>? _firebaseEventSubscription;
  StreamSubscription<NetworkEvent>? _networkEventSubscription;
  StreamSubscription<Map<String, dynamic>?>? _fBroadcastEventSubscription;
  StreamSubscription<Map<String, dynamic>?>? _fReceiveForegroundMasterIdSubscription;
  StreamSubscription<Map<String, dynamic>?>? _fRetrieveMainMasterIdSubscription;
  StreamSubscription<Map<String, dynamic>?>? _fReceiveForegroundIsReadySubscription;
  StreamSubscription<Map<String, dynamic>?>? _fInformStopSubscription;

  VoipState? lastVoipState;
  VoipState? foregroundVoipState;
  String? _mainMasterId;
  bool _foregroundIsReady = false;
  String? _foregroundMasterId;
  bool _initializing = false;
  bool _reconnecting = false;
  bool acceptingCall = false;
  bool acceptedCall = false;

  void fireVoipState(VoipState voipState) {
    _eventBus.fire(VoipEvent(voipState));
    lastVoipState = voipState;
  }

  Future<void> init({
    String? sipWsUrl,
    String? sipNumber,
    String? sipDomain,
    String? sipProxy,
    String? sipSecret,
    String? sipName,
  }) async {
    await prefs.reload();
    sipWsUrl ??= prefs.getString(CacheKeys.sipWsUrl);
    sipNumber ??= prefs.getString(CacheKeys.sipNumber);
    sipDomain ??= prefs.getString(CacheKeys.sipDomain);
    sipProxy ??= prefs.getString(CacheKeys.sipProxy);
    sipSecret ??= prefs.getString(CacheKeys.sipPwd);
    sipName ??= prefs.getString(CacheKeys.sipName);
    if ((sipWsUrl == null &&
        sipNumber == null &&
        sipDomain == null &&
        sipProxy == null &&
        sipSecret == null &&
        sipName == null)) return;

    if (_initializing) return;

    if (await ForegroundService.isRunning()) return;

    try {
      fireVoipState(const VoipSipRegistering());
      _initializing = true;

      bool hasInit = await janusService.init(sipWsUrl);
      if (!hasInit) return;

      janusService.mediaOccupiedCallback('callkit_ringing', () async {
        if (isIOS) {
          return callkitService.isRinging || _audioSessionService.interrupted;
        } else if (isAndroid) {
          return callkitService.isRinging;
        } else {
          return false;
        }
      });

      await janusService.initMedia();

      janusService.addJanusMessageListener(
        kVoipServiceJanusEventPriority,
        _onJanusEvent,
        onError: _onJanusError,
      );

      try {
        String? token;
        if (isMobile) {
          token = await _firebaseService.getFcmtoken();
        } else {
          token = 'desktop';
        }
        token ??= prefs.getString(CacheKeys.sipHeaderToken);
        await janusService.register('sip:$sipNumber@$sipDomain', 'sip:$sipProxy', sipSecret!, sipName, token);
      } catch (e) {
        log.e('Failed to register janus | username - $sipNumber@$sipDomain | proxy - sip:$sipProxy', error: e);
        fireVoipState(const VoipSipRegisteredError());
      }

      retrieveForegroundState();
    } catch (e) {
      log.e('Failed in VoipService Init', error: e);
      fireVoipState(const VoipSipRegisteredError(statusMessage: 'Failed to create session and register'));
    } finally {
      _initializing = false;
    }
  }

  void _onJanusEvent(TypedEvent<JanusEvent> event) async {
    Object data = event.event.plugindata?.data;
    log.t('voip service janus listener data: $data');
    if (data is SipRegisteredEvent) {
      if (data.result?.masterId != null) _mainMasterId = '${data.result!.masterId!}';
      fireVoipState(const VoipSipRegistered());
    } else if (data is SipIncomingCallEvent) {
      String callerId = JanusService.extractExtFromUsername('${data.result?.username!}');
      String caller = '${data.result?.displayname!.replaceAll('"', '')}';
      prefs.remove(CacheKeys.callkitStartCallTime);
      callkitService.setActiveCallUuid();
      fireVoipState(VoipSipIncomingCall(
          statusMessage: VoipSipEvent.incomingCall.statusMessage(), callerId: callerId, caller: caller));
    } else if (data is SipAcceptedEvent) {
      await _onJanusSipAcceptedEvent();
    } else if (data is SipProgressEvent) {
      await _onJanusSipProgressEvent();
    } else if (data is SipCallingEvent) {
      await _onJanusSipCallingEvent();
    } else if (data is SipProceedingEvent) {
      await _onJanusSipProceedingEvent();
    } else if (data is SipRingingEvent) {
      await _onJanusSipRingingEvent();
    } else if (data is SipHangupEvent) {
      await _onJanusSipHangUpEvent();
    } else if (data is SipUnRegisteredEvent) {
      _mainMasterId = null;
      fireVoipState(const VoipSipUnregistered());
    } else if (data is SipTransferCallEvent) {
      await _onJanusTransferCallEvent();
    } else if (data is SipMissedCallEvent) {}
    log.t('voip service janus listener completed');
  }

  Future<void> _onJanusSipAcceptedEvent() async {
    await Future.delayed(const Duration(milliseconds: 200));
    acceptedCall = true;
    callkitService.hideNotificationCall();
    _configureDefaultAudioDevice();
    _audioDeviceService.startDeviceMonitoring();
    prefs.setInt(CacheKeys.callkitStartCallTime, DateTime.now().millisecondsSinceEpoch);
    fireVoipState(VoipSipAcceptedSuccess(
        statusMessage: VoipSipEvent.accepted.statusMessage(),
        caller: lastVoipState?.caller,
        callerId: lastVoipState?.callerId,
        callee: lastVoipState?.callee,
        calleeId: lastVoipState?.callee));
  }

  Future<void> _onJanusSipProgressEvent() async {
    fireVoipState(VoipSipProgress(
        statusMessage: VoipSipEvent.progress.statusMessage(),
        caller: lastVoipState?.caller,
        callerId: lastVoipState?.callerId,
        callee: lastVoipState?.callee,
        calleeId: lastVoipState?.callee));
  }

  Future<void> _onJanusSipCallingEvent() async {
    prefs.remove(CacheKeys.callkitStartCallTime);
    Map<String, String> callInfo = await callkitService.getCallerInfo();
    fireVoipState(VoipSipCalling(
        statusMessage: VoipSipEvent.calling.statusMessage(),
        calleeId: callInfo['callerId'],
        callee: callInfo['caller']));
  }

  Future<void> _onJanusSipProceedingEvent() async {
    fireVoipState(VoipSipProceeding(
        statusMessage: VoipSipEvent.proceeding.statusMessage(),
        caller: lastVoipState?.caller,
        callerId: lastVoipState?.callerId,
        callee: lastVoipState?.callee,
        calleeId: lastVoipState?.callee));
  }

  Future<void> _onJanusSipRingingEvent() async {
    fireVoipState(VoipSipRinging(
        caller: lastVoipState?.caller,
        callerId: lastVoipState?.callerId,
        callee: lastVoipState?.callee,
        calleeId: lastVoipState?.callee));
  }

  Future<void> _onJanusSipHangUpEvent() async {
    callkitService.endCall();
    _restoreSpeaker();
    _audioDeviceService.stopDeviceMonitoring();
    acceptedCall = false;
    prefs.remove(CacheKeys.callkitStartCallTime);

    // Wait a bit before dispose. Sometimes there are something still need to be
    // written to websocket or something duno what after hang up. Let it finish before dispose.
    await Future.delayed(const Duration(milliseconds: 500));
    await dispose();
    _audioSessionService.resetSession();
    fireVoipState(VoipSipHungUp(statusMessage: VoipSipEvent.hangup.statusMessage()));

    if (await isAppOpen()) {
      init();
    }
  }

  Future<void> _onJanusTransferCallEvent() async {
    fireVoipState(const VoipSipTransferCall());
  }

  void _onJanusError(dynamic error) async {
    log.e('janus message listener error', error: error);
    if (error is JanusError) {}
  }

  Future<void> dispose() async {
    acceptedCall = false;
    _mainMasterId = null;
    await janusService.dispose();
    janusService.removeJanusMessageListener(kVoipServiceJanusEventPriority, _onJanusEvent, onError: _onJanusError);
  }

  Future<void> disposeWhenIsMobileAndNoCalls() async {
    if (!isMobile) return;
    bool hasActiveCalls = await callkitService.hasActiveCalls();
    if (hasActiveCalls) {
      await Future.delayed(const Duration(milliseconds: 3000));
      JanusCallStatus janusCallStatus = await janusService.checkJanusCallStatus();
      if (janusCallStatus == JanusCallStatus.nocall && !await ForegroundService.isRunning()) {
        log.t('disposeWhenIsMobileAndNoCalls - callkit has call - janus no call - endcallkit - dispose');
        callkitService.endAllCall();
        dispose();
      }
    } else {
      log.t('disposeWhenIsMobileAndNoCalls - callkit no call - dispose');
      dispose();
    }
  }

  Future<void> maintainConnection() async {
    if (!userHasLoggedIn()) return;
    if (_reconnecting || _initializing || await ForegroundService.isRunning()) return;
    _reconnecting = true;
    try {
      bool janusIsConnected = await janusService.checkJanusConnection();
      log.t('VoipService - maintainConnection - janusIsConnected:$janusIsConnected');
      if (!janusIsConnected) {
        await dispose();
        fireVoipState(const VoipSipUnregistered());
        await init();
      }
    } finally {
      // wait a while then only release the flag, this is to ensure Janus server side has registered
      // before disposing it again.
      await Future.delayed(const Duration(milliseconds: 500));
      _reconnecting = false;
    }
  }

  void _configureDefaultAudioDevice() async {
    if (isAndroid) {
      // based on device priority, select the default audio device.
      final devices = await _audioDeviceService.getAvailableDevices();
      final defaultDevice = _audioDeviceService.getDefaultDevice(devices);
      if (defaultDevice != null) {
        _audioDeviceService.selectDevice(defaultDevice);
        log.t('Selected to use ${defaultDevice.label} as default audio device');
      }
    }
  }

  void _restoreSpeaker() async {
    if (isAndroid) {
      final devices = await _audioDeviceService.getAvailableDevices();
      final loudspeakerDevice = devices.firstWhere(
        (d) => d.deviceId == _audioDeviceService.getLoudSpeakerDeviceId(),
        orElse: () => devices.first,
      );
      _audioDeviceService.selectDevice(loudspeakerDevice);
      log.t('Restored to use loud speaker after call');
    }
  }

  Future<bool> waitUntilConnected() => janusService.waitJanusConnect();

  Future<void> makeCall(String extNum, String sipDomain, String ctcName) async {
    try {
      fireVoipState(
          VoipSipCalling(statusMessage: VoipSipEvent.calling.statusMessage(), calleeId: extNum, callee: ctcName));
      if (isAndroid) {
        if (await recreateSessionInForeground()) {
          await ForegroundService.invokeMethod(
              ForegroundMethod.makeCall, {'extNum': extNum, 'sipDomain': sipDomain, 'ctcName': ctcName});
        } else {
          ForegroundService.invokeMethod(ForegroundMethod.stop);
        }
      } else {
        await janusService.initLocalStream();
        janusService.makeCall(extNum, sipDomain);
      }

      _hiveService.addData<CallRecords>(
        data: CallRecords(
          contactName: ctcName,
          did: extNum,
          duration: '0:00',
          type: CallType.outgoing,
          datetime: DateTime.now(),
        ),
      );

      callkitService.outgoingCall(ctcName, extNum);
    } catch (e) {
      log.e('Failed to make call', error: e);
      _audioSessionService.resetSession();
      callkitService.endCall();
      ForegroundService.invokeMethod(ForegroundMethod.stop);
      fireVoipState(VoipConnectionError(statusMessage: e.toString()));
    }
  }

  Future<void> acceptCall() async {
    if (acceptingCall || acceptedCall) return;
    try {
      acceptingCall = true;
      if (isMobile) {
        Map<String, String> callInfo = await callkitService.getCallerInfo();
        fireVoipState(VoipSipAcceptedLoading(
            statusMessage: VoipSipEvent.loading.statusMessage(),
            caller: callInfo['caller'],
            callerId: callInfo['callerId']));
      }
      if (isAndroid) {
        await ForegroundService.invokeMethod(ForegroundMethod.acceptCall);
      } else {
        await janusService.initLocalStream();
        await janusService.acceptCall();
      }
      callkitService.acceptedCall = true;
      acceptedCall = true;
    } catch (e) {
      log.e('Failed to accept call', error: e);
      fireVoipState(VoipSipAcceptedError(statusMessage: e.toString()));
      callkitService.endCall();
      _audioSessionService.resetSession();
      acceptedCall = false;
    } finally {
      acceptingCall = false;
    }
  }

  Future<void> transferCall(String extNum, String sipDomain) async {
    try {
      await janusService.transferCall(extNum, sipDomain);
    } catch (e) {
      log.e('Failed to transfer call', error: e);
    }
  }

  Future<void> declineCall() async {
    try {
      if (isAndroid) {
        await ForegroundService.invokeMethod(ForegroundMethod.declineCall);
      } else {
        fireVoipState(VoipSipHangingUp(statusMessage: VoipSipEvent.hangingup.statusMessage()));
        await janusService.declineCall();
      }
    } catch (e) {
      log.e('Failed to decline call', error: e);
      callkitService.endCall();
    }
  }

  Future<void> holdCall() async {
    if (isAndroid) {
      await ForegroundService.invokeMethod(ForegroundMethod.holdCall);
    } else {
      await janusService.holdCall();
    }
  }

  Future<void> unholdCall() async {
    if (isAndroid) {
      await ForegroundService.invokeMethod(ForegroundMethod.unholdCall);
    } else {
      await janusService.unholdCall();
    }
  }

  Future<void> toggleHoldCall() async {
    if (isAndroid) {
      await ForegroundService.invokeMethod(ForegroundMethod.toggleHoldCall);
    } else {
      await janusService.toggleHoldCall();
    }
  }

  Future<void> hangupCall() async {
    try {
      if (isAndroid) {
        await ForegroundService.invokeMethod(ForegroundMethod.hangupCall);
      } else {
        fireVoipState(VoipSipHangingUp(statusMessage: VoipSipEvent.hangingup.statusMessage()));
        await janusService.hangupCall();
      }
    } catch (e) {
      log.e('Failed to hangup call', error: e);
      fireVoipState(VoipSipHangupError(statusMessage: e.toString()));
      callkitService.endCall();
      acceptedCall = false;
    }
  }

  Future<void> muteMicAudio(bool mute) async {
    if (isAndroid) {
      await ForegroundService.invokeMethod(ForegroundMethod.muteMicAudio, {'mute': mute});
    } else {
      await janusService.muteMicAudio(mute);
    }
  }

  Future<void> muteSpeakerAudio(bool mute) async {
    if (isAndroid) {
      await ForegroundService.invokeMethod(ForegroundMethod.muteSpeakerAudio, {'mute': mute});
    } else {
      await janusService.muteSpeakerAudio(mute);
    }
  }

  Future<void> iosCallToggleMic(bool isMuted) async {
    await janusService.muteMicAudio(isMuted);
  }

  Future<void> retrieveForegroundState() async {
    if (!isAndroid) return;
    ForegroundService.invokeMethod(ForegroundMethod.retrieveState);
  }

  Future<String?> getForegroundMasterId() async {
    if (!isAndroid) return null;
    await ForegroundService.invokeMethod(ForegroundMethod.retrieveForegroundMasterId);
    bool waitResult =
        await waitForCondition(() async => _foregroundMasterId != null, timeout: const Duration(milliseconds: 500));
    log.t('getForegroundMasterId: $waitResult, _foregroundMasterId: $_foregroundMasterId');
    return _foregroundMasterId;
  }

  Future<bool> recreateSessionInForeground() async {
    await Future.wait([
      dispose(),
      ForegroundService.start(),
    ]);
    await ForegroundService.invokeMethod(ForegroundMethod.retrieveForegroundIsReady);
    bool waitResult =
        await waitForCondition(() async => _foregroundIsReady, timeout: const Duration(milliseconds: 5000));
    log.t('recreateSessionInForeground waitResult:$waitResult');
    return waitResult;
  }

  void _hangupCallWhenNetworkDisconnect() async {
    if (await callkitService.hasActiveCalls()) {
      await hangupCall();
    }
  }

  // NOT BEING USED ANYMORE, BUT LEAVE HERE FOR EASY FUTURE REFERENCE
  // void toggleLocalStream(bool enabled) {
  //   janusService.localStream?.getAudioTracks()[0].enabled = enabled;
  // }

  // void toggleLocalStreamSpeaker(bool enabled) {
  //   janusService.localStream?.getAudioTracks()[0].enableSpeakerphone(enabled);
  // }
}
