import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/shared_preferences_service.dart';
import 'package:ddone/utils/logger_util.dart';

mixin PrefsAware {
  SharedPreferencesService prefs = sl.get<SharedPreferencesService>();

  bool userHasLoggedIn() {
    // only after login we have this stored in sharedPreferences
    return prefs.getString(CacheKeys.sipDomain) != null;
  }

  Future<bool> isAppOpen() async {
    await prefs.reload();
    bool? appOpen = prefs.getBool(CacheKeys.appOpen);
    // first start will be null, subsequently it will be true/false based on AppLifecycleState
    bool result = appOpen == null || appOpen;
    log.t('isAppOpen: $result');
    return result;
  }

  Future<void> resetPrefs() async {
    await Future.wait([
      prefs.remove(CacheKeys.sipProxy),
      prefs.remove(CacheKeys.sipDomain),
      prefs.remove(CacheKeys.sipNumber),
      prefs.remove(CacheKeys.sipName),
      prefs.remove(CacheKeys.sipPwd),
      prefs.remove(CacheKeys.sipContactUrlBase),
      prefs.remove(CacheKeys.sipContactUrlParams),
      prefs.remove(CacheKeys.sipWsUrl),
      prefs.remove(CacheKeys.pnUrl),
      prefs.remove(CacheKeys.sipHeaderToken),
      prefs.remove(CacheKeys.isMessageHistory),
      prefs.remove(CacheKeys.themeKey),
      prefs.remove(CacheKeys.callkitStartCallTime),
      prefs.remove(CacheKeys.missCall),
      prefs.remove(CacheKeys.missCallName),
      prefs.remove(CacheKeys.missCallId),
      prefs.remove(CacheKeys.missCallTime),
      prefs.remove(CacheKeys.firebaseMessageId),
      prefs.remove(CacheKeys.jidResourceId),
    ]);
  }
}
